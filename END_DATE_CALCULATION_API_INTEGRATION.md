# End Date Calculation API Integration

This document explains how to use the end date calculation API integration in the StartWell Flutter app.

## Overview

The end date calculation API integration allows the app to calculate accurate end dates for meal plans based on:
- Company ID (hardcoded as 8163)
- Start date (selected by user)
- Plan ID (from duration API)
- Unit ID (same as company ID)
- Selected weekdays (custom days array)

## API Endpoint

```
POST http://*************:8005/api/v2/admin/settings/calculate-end-date
```

### Request Body

```json
{
  "company_id": 8163,
  "start_date": "2025-08-15",
  "plan_id": 1,
  "unit_id": 8163,
  "days": [1, 3, 5]
}
```

### Parameters

- `company_id` (integer, required): Always set to 8163
- `start_date` (string, required): Start date in YYYY-MM-DD format
- `plan_id` (integer, required): Plan ID from the duration API
- `unit_id` (integer, required): Same value as company_id (8163)
- `days` (array, required): Selected weekdays where 1=Monday, 2=Tuesday, ..., 7=Sunday

### Response Format

```json
{
  "success": true,
  "message": "End date calculated successfully",
  "data": {
    "end_date": "2025-09-15",
    "total_days": 31,
    "delivery_days": 15
  }
}
```

## Usage in Flutter

### 1. Basic Usage

```dart
import 'package:startwell/services/end_date_calculation_service.dart';

// Calculate end date for a meal plan
final response = await EndDateCalculationService.calculateEndDate(
  startDate: DateTime(2025, 8, 15),
  planId: 1,
  selectedDays: [1, 3, 5], // Monday, Wednesday, Friday
);

if (response.success) {
  print('End date: ${response.endDate}');
  print('Start date: ${response.startDate}');
  print('Plan ID: ${response.planId}');
}
```

### 2. Converting Weekday Selection

```dart
// Convert boolean array to days array
final selectedWeekdays = [true, false, true, false, true, false, false];
final days = EndDateCalculationService.convertSelectedWeekdaysToDays(selectedWeekdays);
// Result: [1, 3, 5] for Monday, Wednesday, Friday

// Use in API call
final response = await EndDateCalculationService.calculateEndDate(
  startDate: DateTime(2025, 8, 15),
  planId: 1,
  selectedDays: days,
);
```

### 3. Error Handling

```dart
try {
  final response = await EndDateCalculationService.calculateEndDate(
    startDate: DateTime(2025, 8, 15),
    planId: 1,
    selectedDays: [1, 2, 3, 4, 5],
  );
  
  // Use the calculated end date
  print('Calculated end date: ${response.endDate}');
} catch (e) {
  print('Error calculating end date: $e');
  // Fall back to local calculation
}
```

## Integration with Subscription Screen

The end date calculation is automatically integrated into the subscription selection screen:

### Automatic Calculation

The API is called automatically when:
- User selects a start date from the calendar
- User changes the selected plan/duration
- User toggles custom weekdays (in custom plan mode)

### UI Indicators

The UI shows:
- Loading spinner while calculating
- "Calculated via API" when successful
- "Calculated locally (fallback)" when API fails
- "API Error: Using fallback calculation" when there's an error

### Code Integration

```dart
// In subscription_selection_screen.dart

// Called when date is selected
void _onDaySelected(DateTime selectedDay, DateTime focusedDay) {
  setState(() {
    _startDate = selectedDay;
    _focusedCalendarDate = focusedDay;
    _calculateMealDates();
  });
  
  // Calculate end date with API
  _calculateEndDateWithAPI();
}

// Called when plan changes
void _selectPlan(int index) {
  setState(() {
    _selectedPlanIndex = index;
    _calculateMealDates();
  });
  
  // Calculate end date with API
  _calculateEndDateWithAPI();
}
```

## Configuration

The service is configured with hardcoded values as per requirements:

```dart
class EndDateCalculationService {
  static const String baseUrl = 'http://*************:8005/api/v2/admin/settings';
  static const int companyId = 8163;
  static const int unitId = 8163; // Same as company_id
}
```

## Response Model

The `EndDateCalculationResponse` class provides:

```dart
class EndDateCalculationResponse {
  final bool success;
  final String message;
  final DateTime endDate;
  final DateTime startDate;
  final int planId;
  final List<int> selectedDays;
  final Map<String, dynamic>? metadata;
  
  // Methods
  Map<String, dynamic> toJson();
  factory EndDateCalculationResponse.fromJson(Map<String, dynamic> json);
  String toString();
}
```

## Testing

### Unit Tests

Run the end date calculation service tests:

```bash
flutter test test/services/end_date_calculation_service_test.dart
```

### Manual Testing

1. Open the subscription selection screen
2. Select different start dates and observe the end date calculation
3. Change plans and verify the end date updates
4. Toggle custom weekdays and check the recalculation
5. Monitor the UI indicators for API status

## Troubleshooting

### Common Issues

1. **Network Error**: Check if API server is running on `http://*************:8005`
2. **Invalid Plan ID**: Ensure the plan duration API is working and returning valid plan IDs
3. **Empty Days Array**: Make sure at least one weekday is selected
4. **Date Format Error**: Verify start date is in correct YYYY-MM-DD format

### Debug Steps

1. **Check API Configuration**:
   ```dart
   final isValid = EndDateCalculationService.validateApiConfiguration();
   print('API config valid: $isValid');
   ```

2. **Monitor Logs**: Look for debug logs with prefixes:
   - `🗓️` for calculation start
   - `✅` for successful calculation
   - `❌` for errors

3. **Test Weekday Conversion**:
   ```dart
   final days = EndDateCalculationService.convertSelectedWeekdaysToDays([true, false, true, false, true, false, false]);
   print('Converted days: $days'); // Should be [1, 3, 5]
   ```

## Integration with Working Days API

The end date calculation works in conjunction with the working days API:

1. **Working Days API**: Determines which dates are available for selection
2. **End Date Calculation API**: Calculates the end date based on selected start date and plan

Both APIs are called automatically during month navigation and plan selection to provide a seamless user experience.
