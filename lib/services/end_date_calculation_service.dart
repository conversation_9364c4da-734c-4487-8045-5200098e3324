import 'dart:convert';
import 'dart:developer' as dev;
import 'package:http/http.dart' as http;
import 'package:intl/intl.dart';

/// Service for calculating end dates using the backend API
class EndDateCalculationService {
  static const String baseUrl = 'http://192.168.1.167:8005/api/v2/admin/settings';
  static const int companyId = 8163;
  static const int unitId = 8163; // Same as company_id as per requirements

  /// Calculate end date based on start date, plan, and selected days
  /// 
  /// Parameters:
  /// - [startDate]: The selected start date from the calendar
  /// - [planId]: The plan ID from the duration API
  /// - [selectedDays]: Array of selected weekdays (1-7, where 1=Monday)
  /// 
  /// Returns the calculated end date from the API
  static Future<EndDateCalculationResponse> calculateEndDate({
    required DateTime startDate,
    required int planId,
    required List<int> selectedDays,
  }) async {
    try {
      dev.log('🗓️ Starting end date calculation...');
      dev.log('   📅 Start Date: ${DateFormat('yyyy-MM-dd').format(startDate)}');
      dev.log('   📋 Plan ID: $planId');
      dev.log('   📅 Selected Days: $selectedDays');
      dev.log('   🏢 Company ID: $companyId');
      dev.log('   🏭 Unit ID: $unitId');

      // Build the request URL
      final String url = '$baseUrl/calculate-end-date';
      dev.log('🌐 API Endpoint: $url');

      // Build the request body
      final Map<String, dynamic> requestBody = {
        'company_id': companyId,
        'start_date': DateFormat('yyyy-MM-dd').format(startDate),
        'plan_id': planId,
        'unit_id': unitId,
        'days': selectedDays,
      };

      dev.log('📤 Request Body: ${jsonEncode(requestBody)}');

      // Make the POST request
      final response = await http.post(
        Uri.parse(url),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: jsonEncode(requestBody),
      );

      dev.log('📥 Response Status: ${response.statusCode}');
      dev.log('📥 Response Body: ${response.body}');

      if (response.statusCode == 200) {
        final Map<String, dynamic> jsonResponse = jsonDecode(response.body);
        
        // Parse the response
        final bool success = jsonResponse['success'] ?? false;
        final String message = jsonResponse['message'] ?? '';
        final dynamic data = jsonResponse['data'];

        if (success && data != null) {
          // Extract the end date from the response
          final String? endDateString = data['end_date']?.toString();
          
          if (endDateString != null && endDateString.isNotEmpty) {
            try {
              // Parse the end date
              final DateTime endDate = DateTime.parse(endDateString);
              
              dev.log('✅ End date calculation successful!');
              dev.log('   📅 Calculated End Date: ${DateFormat('yyyy-MM-dd').format(endDate)}');
              
              return EndDateCalculationResponse(
                success: true,
                message: message,
                endDate: endDate,
                startDate: startDate,
                planId: planId,
                selectedDays: selectedDays,
                metadata: data,
              );
            } catch (dateParseError) {
              dev.log('❌ Error parsing end date: $dateParseError');
              throw Exception('Invalid end date format received from API: $endDateString');
            }
          } else {
            dev.log('❌ No end date in API response');
            throw Exception('No end date returned from API');
          }
        } else {
          dev.log('❌ API returned unsuccessful response');
          throw Exception(message.isNotEmpty ? message : 'API returned unsuccessful response');
        }
      } else {
        dev.log('❌ HTTP Error: ${response.statusCode}');
        dev.log('❌ Error Body: ${response.body}');
        
        // Try to parse error message from response
        String errorMessage = 'HTTP ${response.statusCode}: Failed to calculate end date';
        try {
          final errorJson = jsonDecode(response.body);
          if (errorJson['message'] != null) {
            errorMessage = errorJson['message'];
          }
        } catch (e) {
          // Use default error message if JSON parsing fails
        }
        
        throw Exception(errorMessage);
      }
    } catch (e) {
      dev.log('❌ End date calculation failed: $e');
      rethrow;
    }
  }

  /// Validate the API configuration
  static bool validateApiConfiguration() {
    final bool isValid = baseUrl.isNotEmpty && 
                        companyId > 0 && 
                        unitId > 0;
    
    dev.log('🔧 End Date API Configuration:');
    dev.log('   🌐 Base URL: $baseUrl');
    dev.log('   🏢 Company ID: $companyId');
    dev.log('   🏭 Unit ID: $unitId');
    dev.log('   ✅ Valid: $isValid');
    
    return isValid;
  }

  /// Convert selected weekdays boolean array to days array for API
  /// 
  /// [selectedWeekdays] - Array of 7 booleans representing Mon-Sun
  /// Returns array of integers (1-7) where 1=Monday, 7=Sunday
  static List<int> convertSelectedWeekdaysToDays(List<bool> selectedWeekdays) {
    final List<int> days = [];
    
    for (int i = 0; i < selectedWeekdays.length && i < 7; i++) {
      if (selectedWeekdays[i]) {
        days.add(i + 1); // Convert 0-based index to 1-based day (1=Monday)
      }
    }
    
    dev.log('🔄 Weekday conversion:');
    dev.log('   📅 Selected Weekdays: $selectedWeekdays');
    dev.log('   📅 Days Array: $days');
    
    return days;
  }
}

/// Response model for end date calculation API
class EndDateCalculationResponse {
  final bool success;
  final String message;
  final DateTime endDate;
  final DateTime startDate;
  final int planId;
  final List<int> selectedDays;
  final Map<String, dynamic>? metadata;

  EndDateCalculationResponse({
    required this.success,
    required this.message,
    required this.endDate,
    required this.startDate,
    required this.planId,
    required this.selectedDays,
    this.metadata,
  });

  @override
  String toString() {
    return 'EndDateCalculationResponse(success: $success, message: $message, '
           'startDate: ${DateFormat('yyyy-MM-dd').format(startDate)}, '
           'endDate: ${DateFormat('yyyy-MM-dd').format(endDate)}, '
           'planId: $planId, selectedDays: $selectedDays)';
  }

  /// Convert to JSON for storage or debugging
  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      'end_date': DateFormat('yyyy-MM-dd').format(endDate),
      'start_date': DateFormat('yyyy-MM-dd').format(startDate),
      'plan_id': planId,
      'selected_days': selectedDays,
      'metadata': metadata,
    };
  }

  /// Create from JSON
  factory EndDateCalculationResponse.fromJson(Map<String, dynamic> json) {
    return EndDateCalculationResponse(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      endDate: DateTime.parse(json['end_date']),
      startDate: DateTime.parse(json['start_date']),
      planId: json['plan_id'] ?? 0,
      selectedDays: List<int>.from(json['selected_days'] ?? []),
      metadata: json['metadata'],
    );
  }
}
